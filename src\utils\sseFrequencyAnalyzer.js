/**
 * SSE 频率分析器
 * 用于分析 SSE 响应频率和 Markdown 解析时机问题
 */

export class SSEFrequencyAnalyzer {
  constructor() {
    this.events = []
    this.renderEvents = []
    this.isAnalyzing = false
    this.analysisResults = {
      sseFrequency: 0,
      averageInterval: 0,
      renderDelay: 0,
      missedRenders: 0,
      recommendations: []
    }
  }

  /**
   * 开始分析
   */
  startAnalysis() {
    this.isAnalyzing = true
    this.events = []
    this.renderEvents = []
    console.log('🔍 SSE频率分析器：开始分析')
  }

  /**
   * 停止分析
   */
  stopAnalysis() {
    this.isAnalyzing = false
    this.generateAnalysisReport()
    console.log('🛑 SSE频率分析器：停止分析')
  }

  /**
   * 记录 SSE 事件
   * @param {string} data - SSE 数据
   * @param {number} timestamp - 时间戳
   */
  recordSSEEvent(data, timestamp = performance.now()) {
    if (!this.isAnalyzing) return

    this.events.push({
      type: 'sse',
      data,
      timestamp,
      dataLength: data?.length || 0
    })

    // 只保留最近 10 秒的数据
    const cutoff = timestamp - 10000
    this.events = this.events.filter(event => event.timestamp > cutoff)
  }

  /**
   * 记录渲染事件
   * @param {number} renderTime - 渲染耗时
   * @param {number} timestamp - 时间戳
   */
  recordRenderEvent(renderTime, timestamp = performance.now()) {
    if (!this.isAnalyzing) return

    this.renderEvents.push({
      type: 'render',
      renderTime,
      timestamp
    })

    // 只保留最近 10 秒的数据
    const cutoff = timestamp - 10000
    this.renderEvents = this.renderEvents.filter(event => event.timestamp > cutoff)
  }

  /**
   * 计算 SSE 频率
   * @param {number} timeWindow - 时间窗口（毫秒）
   * @returns {number} 频率（Hz）
   */
  calculateSSEFrequency(timeWindow = 1000) {
    const now = performance.now()
    const recentEvents = this.events.filter(event => 
      event.type === 'sse' && event.timestamp > now - timeWindow
    )
    return recentEvents.length
  }

  /**
   * 计算平均 SSE 间隔
   * @returns {number} 平均间隔（毫秒）
   */
  calculateAverageSSEInterval() {
    const sseEvents = this.events.filter(event => event.type === 'sse')
    if (sseEvents.length < 2) return 0

    const intervals = []
    for (let i = 1; i < sseEvents.length; i++) {
      intervals.push(sseEvents[i].timestamp - sseEvents[i - 1].timestamp)
    }

    return intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length
  }

  /**
   * 计算渲染延迟
   * @returns {number} 平均渲染延迟（毫秒）
   */
  calculateRenderDelay() {
    const delays = []
    
    this.events.forEach(sseEvent => {
      if (sseEvent.type !== 'sse') return
      
      // 找到最近的渲染事件
      const nearestRender = this.renderEvents.find(renderEvent => 
        renderEvent.timestamp >= sseEvent.timestamp && 
        renderEvent.timestamp <= sseEvent.timestamp + 200 // 200ms 内的渲染
      )
      
      if (nearestRender) {
        delays.push(nearestRender.timestamp - sseEvent.timestamp)
      }
    })

    return delays.length > 0 
      ? delays.reduce((sum, delay) => sum + delay, 0) / delays.length 
      : 0
  }

  /**
   * 检测丢失的渲染
   * @returns {number} 丢失的渲染次数
   */
  detectMissedRenders() {
    let missedCount = 0
    
    this.events.forEach(sseEvent => {
      if (sseEvent.type !== 'sse') return
      
      // 检查是否有对应的渲染事件
      const hasRender = this.renderEvents.some(renderEvent => 
        renderEvent.timestamp >= sseEvent.timestamp && 
        renderEvent.timestamp <= sseEvent.timestamp + 500 // 500ms 内应该有渲染
      )
      
      if (!hasRender) {
        missedCount++
      }
    })

    return missedCount
  }

  /**
   * 生成分析报告
   */
  generateAnalysisReport() {
    const frequency = this.calculateSSEFrequency()
    const averageInterval = this.calculateAverageSSEInterval()
    const renderDelay = this.calculateRenderDelay()
    const missedRenders = this.detectMissedRenders()

    this.analysisResults = {
      sseFrequency: frequency,
      averageInterval: Math.round(averageInterval),
      renderDelay: Math.round(renderDelay),
      missedRenders,
      recommendations: this.generateRecommendations(frequency, averageInterval, renderDelay, missedRenders)
    }

    console.log('📊 SSE频率分析报告:', this.analysisResults)
    return this.analysisResults
  }

  /**
   * 生成优化建议
   */
  generateRecommendations(frequency, averageInterval, renderDelay, missedRenders) {
    const recommendations = []

    // SSE 频率分析
    if (frequency > 20) {
      recommendations.push({
        type: 'warning',
        title: 'SSE 频率过高',
        message: `当前频率 ${frequency}Hz 过高，建议使用 requestAnimationFrame 渲染模式`,
        solution: '切换到 AdaptiveMarkdownRenderer 的高频模式'
      })
    } else if (frequency < 2) {
      recommendations.push({
        type: 'info',
        title: 'SSE 频率较低',
        message: `当前频率 ${frequency}Hz 较低，可以使用立即渲染模式`,
        solution: '切换到 AdaptiveMarkdownRenderer 的低频模式'
      })
    }

    // 间隔分析
    if (averageInterval > 200) {
      recommendations.push({
        type: 'warning',
        title: 'SSE 间隔过大',
        message: `平均间隔 ${averageInterval}ms 过大，可能影响用户体验`,
        solution: '考虑优化后端 SSE 发送频率'
      })
    } else if (averageInterval < 50) {
      recommendations.push({
        type: 'warning',
        title: 'SSE 间隔过小',
        message: `平均间隔 ${averageInterval}ms 过小，可能导致渲染跟不上`,
        solution: '使用队列管理或降低渲染频率'
      })
    }

    // 渲染延迟分析
    if (renderDelay > 100) {
      recommendations.push({
        type: 'error',
        title: '渲染延迟过高',
        message: `平均渲染延迟 ${renderDelay}ms 过高，严重影响实时性`,
        solution: '使用 NativeMarkdownRenderer 或优化渲染逻辑'
      })
    }

    // 丢失渲染分析
    if (missedRenders > 0) {
      recommendations.push({
        type: 'error',
        title: '存在丢失的渲染',
        message: `检测到 ${missedRenders} 次丢失的渲染，可能导致内容不完整`,
        solution: '检查渲染器的事件监听和强制更新机制'
      })
    }

    // 综合建议
    if (frequency > 15 && renderDelay > 50) {
      recommendations.push({
        type: 'suggestion',
        title: '推荐使用自适应渲染器',
        message: '当前场景适合使用 AdaptiveMarkdownRenderer 自动优化',
        solution: '切换到自适应渲染器，让系统自动选择最优策略'
      })
    }

    return recommendations
  }

  /**
   * 获取实时统计
   */
  getRealTimeStats() {
    return {
      sseFrequency: this.calculateSSEFrequency(),
      averageInterval: Math.round(this.calculateAverageSSEInterval()),
      renderDelay: Math.round(this.calculateRenderDelay()),
      totalSSEEvents: this.events.filter(e => e.type === 'sse').length,
      totalRenderEvents: this.renderEvents.length
    }
  }

  /**
   * 清理数据
   */
  clear() {
    this.events = []
    this.renderEvents = []
    this.analysisResults = {
      sseFrequency: 0,
      averageInterval: 0,
      renderDelay: 0,
      missedRenders: 0,
      recommendations: []
    }
  }
}

// 创建全局分析器实例
export const sseAnalyzer = new SSEFrequencyAnalyzer()

/**
 * 便捷函数：开始 SSE 分析
 */
export function startSSEAnalysis() {
  sseAnalyzer.startAnalysis()
}

/**
 * 便捷函数：停止 SSE 分析并获取报告
 */
export function stopSSEAnalysis() {
  return sseAnalyzer.stopAnalysis()
}

/**
 * 便捷函数：记录 SSE 事件
 */
export function recordSSE(data) {
  sseAnalyzer.recordSSEEvent(data)
}

/**
 * 便捷函数：记录渲染事件
 */
export function recordRender(renderTime) {
  sseAnalyzer.recordRenderEvent(renderTime)
}
